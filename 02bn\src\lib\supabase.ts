import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI3MjAsImV4cCI6MTk2MDc2ODcyMH0.placeholder'

// Create a mock client for development when Supabase is not configured
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface Goal {
  id: string
  user_id: string
  title: string
  description?: string
  target_amount?: number
  current_amount?: number
  target_date?: string
  category: 'financial' | 'business' | 'personal' | 'health' | 'learning'
  status: 'active' | 'completed' | 'paused' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  created_at: string
  updated_at: string
}

export interface Habit {
  id: string
  user_id: string
  name: string
  description?: string
  category: 'health' | 'productivity' | 'learning' | 'finance' | 'personal'
  frequency: 'daily' | 'weekly' | 'monthly'
  target_count: number
  current_streak: number
  best_streak: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface HabitEntry {
  id: string
  habit_id: string
  user_id: string
  completed_at: string
  notes?: string
}

export interface FinancialAccount {
  id: string
  user_id: string
  name: string
  type: 'checking' | 'savings' | 'investment' | 'credit' | 'loan' | 'asset'
  balance: number
  currency: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  user_id: string
  account_id: string
  amount: number
  description: string
  category: string
  type: 'income' | 'expense' | 'transfer'
  date: string
  created_at: string
}

export interface LearningResource {
  id: string
  user_id: string
  title: string
  type: 'book' | 'course' | 'article' | 'video' | 'podcast'
  url?: string
  status: 'to_read' | 'reading' | 'completed' | 'paused'
  progress: number
  rating?: number
  notes?: string
  created_at: string
  updated_at: string
}

export interface Affirmation {
  id: string
  user_id: string
  text: string
  category: 'success' | 'confidence' | 'wealth' | 'health' | 'relationships'
  is_favorite: boolean
  created_at: string
}

export interface MoodEntry {
  id: string
  user_id: string
  mood_score: number // 1-10 scale
  energy_level: number // 1-10 scale
  stress_level: number // 1-10 scale
  notes?: string
  date: string
  created_at: string
}
