'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'
import { DemoNotice } from '@/components/ui/DemoNotice'
import { motion } from 'framer-motion'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  BookOpenIcon,
  HeartIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && user) {
      router.push('/dashboard')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-2 border-primary border-t-transparent"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <DemoNotice />
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <div className="flex items-center justify-center gap-4 mb-8">
              <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center elevation-2">
                <span className="text-on-primary font-bold text-2xl">02</span>
              </div>
              <h1 className="text-5xl md:text-7xl font-bold text-on-surface">02Bn</h1>
            </div>

            <h2 className="text-3xl md:text-5xl font-bold text-on-surface mb-6">
              From Zero to <span className="text-primary">Billions</span>
            </h2>

            <p className="text-xl text-on-surface-variant mb-8 max-w-3xl mx-auto">
              Transform your life with the ultimate personal performance tracking and wealth-building platform.
              Set ambitious goals, build powerful habits, and track your journey to financial freedom.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/auth/signup">
                  Get Started Free
                  <ArrowRightIcon className="h-5 w-5" />
                </Link>
              </Button>
              <Button variant="outlined" size="lg" asChild>
                <Link href="/auth/login">
                  Sign In
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-surface">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h3 className="text-3xl md:text-4xl font-bold text-on-surface mb-4">
              Everything you need to succeed
            </h3>
            <p className="text-xl text-on-surface-variant max-w-2xl mx-auto">
              Comprehensive tools designed to help you achieve your most ambitious goals
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: ChartBarIcon,
                title: 'Goal Management',
                description: 'Set, track, and achieve ambitious financial and personal goals with intelligent milestone breakdown.'
              },
              {
                icon: CurrencyDollarIcon,
                title: 'Wealth Tracking',
                description: 'Monitor your net worth, investments, and financial progress with real-time insights and analytics.'
              },
              {
                icon: BookOpenIcon,
                title: 'Learning System',
                description: 'Curate knowledge, track reading progress, and build the skills needed for success.'
              },
              {
                icon: HeartIcon,
                title: 'Habit Building',
                description: 'Develop powerful daily habits and maintain streaks that compound into extraordinary results.'
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-background rounded-xl p-6 elevation-1 hover:elevation-2 transition-all duration-200"
              >
                <feature.icon className="h-12 w-12 text-primary mb-4" />
                <h4 className="text-xl font-semibold text-on-surface mb-2">{feature.title}</h4>
                <p className="text-on-surface-variant">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-3xl md:text-4xl font-bold text-on-surface mb-4">
              Ready to start your journey?
            </h3>
            <p className="text-xl text-on-surface-variant mb-8">
              Join thousands of ambitious individuals who are already building their path to billions.
            </p>
            <Button size="lg" asChild>
              <Link href="/auth/signup">
                Start Building Your Future
                <ArrowRightIcon className="h-5 w-5" />
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
