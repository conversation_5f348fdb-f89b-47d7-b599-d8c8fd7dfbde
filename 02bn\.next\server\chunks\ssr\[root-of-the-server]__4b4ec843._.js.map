{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBsYWNlaG9sZGVyIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NDUxOTI3MjAsImV4cCI6MTk2MDc2ODcyMH0.placeholder'\n\n// Create a mock client for development when Supabase is not configured\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Goal {\n  id: string\n  user_id: string\n  title: string\n  description?: string\n  target_amount?: number\n  current_amount?: number\n  target_date?: string\n  category: 'financial' | 'business' | 'personal' | 'health' | 'learning'\n  status: 'active' | 'completed' | 'paused' | 'cancelled'\n  priority: 'low' | 'medium' | 'high'\n  created_at: string\n  updated_at: string\n}\n\nexport interface Habit {\n  id: string\n  user_id: string\n  name: string\n  description?: string\n  category: 'health' | 'productivity' | 'learning' | 'finance' | 'personal'\n  frequency: 'daily' | 'weekly' | 'monthly'\n  target_count: number\n  current_streak: number\n  best_streak: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface HabitEntry {\n  id: string\n  habit_id: string\n  user_id: string\n  completed_at: string\n  notes?: string\n}\n\nexport interface FinancialAccount {\n  id: string\n  user_id: string\n  name: string\n  type: 'checking' | 'savings' | 'investment' | 'credit' | 'loan' | 'asset'\n  balance: number\n  currency: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface Transaction {\n  id: string\n  user_id: string\n  account_id: string\n  amount: number\n  description: string\n  category: string\n  type: 'income' | 'expense' | 'transfer'\n  date: string\n  created_at: string\n}\n\nexport interface LearningResource {\n  id: string\n  user_id: string\n  title: string\n  type: 'book' | 'course' | 'article' | 'video' | 'podcast'\n  url?: string\n  status: 'to_read' | 'reading' | 'completed' | 'paused'\n  progress: number\n  rating?: number\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface Affirmation {\n  id: string\n  user_id: string\n  text: string\n  category: 'success' | 'confidence' | 'wealth' | 'health' | 'relationships'\n  is_favorite: boolean\n  created_at: string\n}\n\nexport interface MoodEntry {\n  id: string\n  user_id: string\n  mood_score: number // 1-10 scale\n  energy_level: number // 1-10 scale\n  stress_level: number // 1-10 scale\n  notes?: string\n  date: string\n  created_at: string\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,uEAAwC;AAC5D,MAAM,kBAAkB,4MAA6C;AAG9D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>\n  signOut: () => Promise<{ error: any }>\n  signInWithGoogle: () => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if Supa<PERSON> is properly configured\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n    if (!supabaseUrl || supabaseUrl.includes('placeholder')) {\n      // Mock authentication for development\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session)\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }).catch(() => {\n      setLoading(false)\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setSession(session)\n      setUser(session?.user ?? null)\n      setLoading(false)\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signIn = async (email: string, password: string) => {\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n    if (!supabaseUrl || supabaseUrl.includes('placeholder')) {\n      // Mock sign in for development\n      return { error: { message: 'Supabase not configured. This is a demo mode.' } }\n    }\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n      return { error }\n    } catch (error) {\n      return { error: { message: 'Authentication service unavailable' } }\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName?: string) => {\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n    if (!supabaseUrl || supabaseUrl.includes('placeholder')) {\n      // Mock sign up for development\n      return { error: { message: 'Supabase not configured. This is a demo mode.' } }\n    }\n\n    try {\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      })\n      return { error }\n    } catch (error) {\n      return { error: { message: 'Authentication service unavailable' } }\n    }\n  }\n\n  const signOut = async () => {\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n    if (!supabaseUrl || supabaseUrl.includes('placeholder')) {\n      // Mock sign out for development\n      return { error: null }\n    }\n\n    try {\n      const { error } = await supabase.auth.signOut()\n      return { error }\n    } catch (error) {\n      return { error: { message: 'Authentication service unavailable' } }\n    }\n  }\n\n  const signInWithGoogle = async () => {\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n    if (!supabaseUrl || supabaseUrl.includes('placeholder')) {\n      // Mock Google sign in for development\n      return { error: { message: 'Supabase not configured. This is a demo mode.' } }\n    }\n\n    try {\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider: 'google',\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n      return { error }\n    } catch (error) {\n      return { error: { message: 'Authentication service unavailable' } }\n    }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    signInWithGoogle,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C,MAAM;QACN,IAAI,CAAC,eAAe,YAAY,QAAQ,CAAC,gBAAgB;YACvD,sCAAsC;YACtC,WAAW;YACX;QACF;QAEA,sBAAsB;QACtB,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;YACpD,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb,GAAG,KAAK,CAAC;YACP,WAAW;QACb;QAEA,0BAA0B;QAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;YAChD,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAEA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM;QACN,IAAI,CAAC,eAAe,YAAY,QAAQ,CAAC,gBAAgB;YACvD,+BAA+B;YAC/B,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAgD;YAAE;QAC/E;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YACA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAqC;YAAE;QACpE;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM;QACN,IAAI,CAAC,eAAe,YAAY,QAAQ,CAAC,gBAAgB;YACvD,+BAA+B;YAC/B,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAgD;YAAE;QAC/E;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC3C;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YACA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAqC;YAAE;QACpE;IACF;IAEA,MAAM,UAAU;QACd,MAAM;QACN,IAAI,CAAC,eAAe,YAAY,QAAQ,CAAC,gBAAgB;YACvD,gCAAgC;YAChC,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAqC;YAAE;QACpE;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM;QACN,IAAI,CAAC,eAAe,YAAY,QAAQ,CAAC,gBAAgB;YACvD,sCAAsC;YACtC,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAgD;YAAE;QAC/E;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBACpD,UAAU;gBACV,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YACA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAqC;YAAE;QACpE;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}