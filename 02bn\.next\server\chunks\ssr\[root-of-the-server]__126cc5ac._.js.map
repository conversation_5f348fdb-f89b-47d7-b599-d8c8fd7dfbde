{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000000) {\n    return (num / 1000000000).toFixed(1) + 'B'\n  }\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatRelativeTime(date: string | Date): string {\n  const now = new Date()\n  const targetDate = new Date(date)\n  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000)\n\n  if (diffInSeconds < 60) {\n    return 'just now'\n  }\n\n  const diffInMinutes = Math.floor(diffInSeconds / 60)\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes}m ago`\n  }\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) {\n    return `${diffInHours}h ago`\n  }\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) {\n    return `${diffInDays}d ago`\n  }\n\n  return formatDate(date)\n}\n\nexport function calculateProgress(current: number, target: number): number {\n  if (target === 0) return 0\n  return Math.min((current / target) * 100, 100)\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n\nexport function getInitials(name: string): string {\n  return name\n    .split(' ')\n    .map(word => word.charAt(0).toUpperCase())\n    .join('')\n    .slice(0, 2)\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function getGreeting(): string {\n  const hour = new Date().getHours()\n  if (hour < 12) return 'Good morning'\n  if (hour < 18) return 'Good afternoon'\n  return 'Good evening'\n}\n\nexport function getDaysInStreak(dates: string[]): number {\n  if (dates.length === 0) return 0\n  \n  const sortedDates = dates.sort((a, b) => new Date(b).getTime() - new Date(a).getTime())\n  const today = new Date()\n  today.setHours(0, 0, 0, 0)\n  \n  let streak = 0\n  let currentDate = new Date(today)\n  \n  for (const dateStr of sortedDates) {\n    const date = new Date(dateStr)\n    date.setHours(0, 0, 0, 0)\n    \n    if (date.getTime() === currentDate.getTime()) {\n      streak++\n      currentDate.setDate(currentDate.getDate() - 1)\n    } else {\n      break\n    }\n  }\n  \n  return streak\n}\n\nexport function getMotivationalQuote(): string {\n  const quotes = [\n    \"The way to get started is to quit talking and begin doing. - Walt Disney\",\n    \"Innovation distinguishes between a leader and a follower. - Steve Jobs\",\n    \"Your time is limited, don't waste it living someone else's life. - Steve Jobs\",\n    \"Life is what happens to you while you're busy making other plans. - John Lennon\",\n    \"The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt\",\n    \"It is during our darkest moments that we must focus to see the light. - Aristotle\",\n    \"Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill\",\n    \"The only impossible journey is the one you never begin. - Tony Robbins\",\n    \"In the midst of winter, I found there was, within me, an invincible summer. - Albert Camus\",\n    \"Be yourself; everyone else is already taken. - Oscar Wilde\"\n  ]\n  \n  return quotes[Math.floor(Math.random() * quotes.length)]\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,YAAY;QACrB,OAAO,CAAC,MAAM,UAAU,EAAE,OAAO,CAAC,KAAK;IACzC;IACA,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,aAAa,IAAI,KAAK;IAC5B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;IAE1E,IAAI,gBAAgB,IAAI;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC,gBAAgB;IACjD,IAAI,gBAAgB,IAAI;QACtB,OAAO,GAAG,cAAc,KAAK,CAAC;IAChC;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI;QACpB,OAAO,GAAG,YAAY,KAAK,CAAC;IAC9B;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG;QAClB,OAAO,GAAG,WAAW,KAAK,CAAC;IAC7B;IAEA,OAAO,WAAW;AACpB;AAEO,SAAS,kBAAkB,OAAe,EAAE,MAAc;IAC/D,IAAI,WAAW,GAAG,OAAO;IACzB,OAAO,KAAK,GAAG,CAAC,AAAC,UAAU,SAAU,KAAK;AAC5C;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,IACtC,IAAI,CAAC,IACL,KAAK,CAAC,GAAG;AACd;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS;IACd,MAAM,OAAO,IAAI,OAAO,QAAQ;IAChC,IAAI,OAAO,IAAI,OAAO;IACtB,IAAI,OAAO,IAAI,OAAO;IACtB,OAAO;AACT;AAEO,SAAS,gBAAgB,KAAe;IAC7C,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,MAAM,cAAc,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,GAAG,OAAO,KAAK,IAAI,KAAK,GAAG,OAAO;IACpF,MAAM,QAAQ,IAAI;IAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;IAExB,IAAI,SAAS;IACb,IAAI,cAAc,IAAI,KAAK;IAE3B,KAAK,MAAM,WAAW,YAAa;QACjC,MAAM,OAAO,IAAI,KAAK;QACtB,KAAK,QAAQ,CAAC,GAAG,GAAG,GAAG;QAEvB,IAAI,KAAK,OAAO,OAAO,YAAY,OAAO,IAAI;YAC5C;YACA,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;QAC9C,OAAO;YACL;QACF;IACF;IAEA,OAAO;AACT;AAEO,SAAS;IACd,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;AAC1D", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/components/ui/Button.tsx"], "sourcesContent": ["'use client'\n\nimport { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { motion } from 'framer-motion'\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'filled' | 'outlined' | 'text' | 'elevated' | 'tonal'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  icon?: React.ReactNode\n  iconPosition?: 'left' | 'right'\n  asChild?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      className,\n      variant = 'filled',\n      size = 'md',\n      loading = false,\n      icon,\n      iconPosition = 'left',\n      children,\n      disabled,\n      asChild = false,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-full font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ripple'\n\n    const variantClasses = {\n      filled: 'bg-primary text-on-primary hover:shadow-md focus:ring-primary/20 elevation-1 hover:elevation-2',\n      outlined: 'border border-outline text-primary bg-transparent hover:bg-primary/8 focus:ring-primary/20',\n      text: 'text-primary bg-transparent hover:bg-primary/8 focus:ring-primary/20',\n      elevated: 'bg-surface text-on-surface elevation-1 hover:elevation-2 focus:ring-primary/20',\n      tonal: 'bg-secondary-container text-on-secondary-container hover:shadow-sm focus:ring-secondary/20'\n    }\n\n    const sizeClasses = {\n      sm: 'h-8 px-3 text-sm gap-1',\n      md: 'h-10 px-4 text-sm gap-2',\n      lg: 'h-12 px-6 text-base gap-2'\n    }\n\n    if (asChild) {\n      return (\n        <motion.div\n          className={cn(\n            baseClasses,\n            variantClasses[variant],\n            sizeClasses[size],\n            className\n          )}\n          whileTap={{ scale: 0.98 }}\n          whileHover={{ scale: 1.02 }}\n        >\n          {children}\n        </motion.div>\n      )\n    }\n\n    return (\n      <motion.button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          className\n        )}\n        disabled={disabled || loading}\n        whileTap={{ scale: 0.98 }}\n        whileHover={{ scale: 1.02 }}\n        {...props}\n      >\n        {loading && (\n          <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent\" />\n        )}\n        {!loading && icon && iconPosition === 'left' && (\n          <span className=\"flex-shrink-0\">{icon}</span>\n        )}\n        {children}\n        {!loading && icon && iconPosition === 'right' && (\n          <span className=\"flex-shrink-0\">{icon}</span>\n        )}\n      </motion.button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CACE,EACE,SAAS,EACT,UAAU,QAAQ,EAClB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,GAAG,OACJ,EACD;IAEA,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,QAAQ;QACR,UAAU;QACV,MAAM;QACN,UAAU;QACV,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;YAEF,UAAU;gBAAE,OAAO;YAAK;YACxB,YAAY;gBAAE,OAAO;YAAK;sBAEzB;;;;;;IAGP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAEF,UAAU,YAAY;QACtB,UAAU;YAAE,OAAO;QAAK;QACxB,YAAY;YAAE,OAAO;QAAK;QACzB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBAAI,WAAU;;;;;;YAEhB,CAAC,WAAW,QAAQ,iBAAiB,wBACpC,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;YAElC;YACA,CAAC,WAAW,QAAQ,iBAAiB,yBACpC,8OAAC;gBAAK,WAAU;0BAAiB;;;;;;;;;;;;AAIzC;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/components/ui/Card.tsx"], "sourcesContent": ["'use client'\n\nimport { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { motion } from 'framer-motion'\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'elevated' | 'filled' | 'outlined'\n  elevation?: 1 | 2 | 3 | 4 | 5\n  interactive?: boolean\n}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  (\n    {\n      className,\n      variant = 'elevated',\n      elevation = 1,\n      interactive = false,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = 'rounded-xl transition-all duration-200'\n\n    const variantClasses = {\n      elevated: `bg-surface text-on-surface elevation-${elevation}`,\n      filled: 'bg-surface-variant text-on-surface-variant',\n      outlined: 'bg-surface text-on-surface border border-outline'\n    }\n\n    const interactiveClasses = interactive\n      ? 'cursor-pointer hover:elevation-3 hover:scale-[1.02] active:scale-[0.98]'\n      : ''\n\n    return (\n      <motion.div\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variantClasses[variant],\n          interactiveClasses,\n          className\n        )}\n        initial={interactive ? { scale: 1 } : undefined}\n        whileHover={interactive ? { scale: 1.02 } : undefined}\n        whileTap={interactive ? { scale: 0.98 } : undefined}\n        {...props}\n      >\n        {children}\n      </motion.div>\n    )\n  }\n)\n\nCard.displayName = 'Card'\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  title?: string\n  subtitle?: string\n  action?: React.ReactNode\n}\n\nconst CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, title, subtitle, action, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex items-start justify-between p-6 pb-4', className)}\n        {...props}\n      >\n        <div className=\"flex-1\">\n          {title && (\n            <h3 className=\"text-lg font-medium text-on-surface\">{title}</h3>\n          )}\n          {subtitle && (\n            <p className=\"text-sm text-on-surface-variant mt-1\">{subtitle}</p>\n          )}\n          {children}\n        </div>\n        {action && <div className=\"ml-4\">{action}</div>}\n      </div>\n    )\n  }\n)\n\nCardHeader.displayName = 'CardHeader'\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nconst CardContent = forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('px-6 pb-6', className)}\n        {...props}\n      />\n    )\n  }\n)\n\nCardContent.displayName = 'CardContent'\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {}\n\nconst CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex items-center justify-end gap-2 px-6 py-4 border-t border-outline-variant', className)}\n        {...props}\n      />\n    )\n  }\n)\n\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardContent, CardFooter }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAYA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CACE,EACE,SAAS,EACT,UAAU,UAAU,EACpB,YAAY,CAAC,EACb,cAAc,KAAK,EACnB,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,UAAU,CAAC,qCAAqC,EAAE,WAAW;QAC7D,QAAQ;QACR,UAAU;IACZ;IAEA,MAAM,qBAAqB,cACvB,4EACA;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,oBACA;QAEF,SAAS,cAAc;YAAE,OAAO;QAAE,IAAI;QACtC,YAAY,cAAc;YAAE,OAAO;QAAK,IAAI;QAC5C,UAAU,cAAc;YAAE,OAAO;QAAK,IAAI;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAQnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC3D,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;oBAEtD,0BACC,8OAAC;wBAAE,WAAU;kCAAwC;;;;;;oBAEtD;;;;;;;YAEF,wBAAU,8OAAC;gBAAI,WAAU;0BAAQ;;;;;;;;;;;;AAGxC;AAGF,WAAW,WAAW,GAAG;AAIzB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;;;;;;AAGf;AAGF,YAAY,WAAW,GAAG;AAI1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAC1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iFAAiF;QAC9F,GAAG,KAAK;;;;;;AAGf;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/components/ui/Badge.tsx"], "sourcesContent": ["'use client'\n\nimport { forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'success' | 'warning' | 'error' | 'info'\n  size?: 'sm' | 'md' | 'lg'\n  icon?: React.ReactNode\n}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  (\n    {\n      className,\n      variant = 'default',\n      size = 'md',\n      icon,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = 'inline-flex items-center gap-1 font-medium rounded-full'\n\n    const variantClasses = {\n      default: 'bg-surface-variant text-on-surface-variant',\n      success: 'bg-google-green/10 text-google-green border border-google-green/20',\n      warning: 'bg-google-yellow/10 text-google-yellow border border-google-yellow/20',\n      error: 'bg-error/10 text-error border border-error/20',\n      info: 'bg-google-blue/10 text-google-blue border border-google-blue/20'\n    }\n\n    const sizeClasses = {\n      sm: 'px-2 py-0.5 text-xs',\n      md: 'px-3 py-1 text-sm',\n      lg: 'px-4 py-1.5 text-base'\n    }\n\n    const iconSizeClasses = {\n      sm: 'h-3 w-3',\n      md: 'h-4 w-4',\n      lg: 'h-5 w-5'\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          className\n        )}\n        {...props}\n      >\n        {icon && (\n          <span className={iconSizeClasses[size]}>\n            {icon}\n          </span>\n        )}\n        {children}\n      </div>\n    )\n  }\n)\n\nBadge.displayName = 'Badge'\n\nexport { Badge }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CACE,EACE,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,IAAI,EACJ,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;;YAER,sBACC,8OAAC;gBAAK,WAAW,eAAe,CAAC,KAAK;0BACnC;;;;;;YAGJ;;;;;;;AAGP;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/components/ui/DemoNotice.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent } from './Card'\nimport { Button } from './Button'\nimport { Badge } from './Badge'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { XMarkIcon, InformationCircleIcon } from '@heroicons/react/24/outline'\n\nexport function DemoNotice() {\n  const [isVisible, setIsVisible] = useState(true)\n\n  if (!isVisible) return null\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0, y: -50 }}\n        animate={{ opacity: 1, y: 0 }}\n        exit={{ opacity: 0, y: -50 }}\n        className=\"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md px-4\"\n      >\n        <Card className=\"border border-google-blue/20 bg-google-blue/5\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-start gap-3\">\n              <InformationCircleIcon className=\"h-5 w-5 text-google-blue flex-shrink-0 mt-0.5\" />\n              <div className=\"flex-1\">\n                <div className=\"flex items-center gap-2 mb-1\">\n                  <h4 className=\"font-medium text-on-surface\">Demo Mode</h4>\n                  <Badge variant=\"info\" size=\"sm\">Preview</Badge>\n                </div>\n                <p className=\"text-sm text-on-surface-variant\">\n                  This is a demo version. To use real authentication and data storage, \n                  configure Supabase in your environment variables.\n                </p>\n              </div>\n              <Button\n                variant=\"text\"\n                size=\"sm\"\n                onClick={() => setIsVisible(false)}\n                icon={<XMarkIcon className=\"h-4 w-4\" />}\n              />\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,WAAU;sBAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,yOAAA,CAAA,wBAAqB;gCAAC,WAAU;;;;;;0CACjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAO,MAAK;0DAAK;;;;;;;;;;;;kDAElC,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;0CAKjD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa;gCAC5B,oBAAM,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/bn02/02bn/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\nimport { DemoNotice } from '@/components/ui/DemoNotice'\nimport { motion } from 'framer-motion'\nimport {\n  ChartBarIcon,\n  CurrencyDollarIcon,\n  BookOpenIcon,\n  HeartIcon,\n  ArrowRightIcon\n} from '@heroicons/react/24/outline'\n\nexport default function Home() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && user) {\n      router.push('/dashboard')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-background flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-2 border-primary border-t-transparent\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <DemoNotice />\n      {/* Hero Section */}\n      <section className=\"relative overflow-hidden\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5\"></div>\n        <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-center\"\n          >\n            <div className=\"flex items-center justify-center gap-4 mb-8\">\n              <div className=\"w-16 h-16 bg-primary rounded-2xl flex items-center justify-center elevation-2\">\n                <span className=\"text-on-primary font-bold text-2xl\">02</span>\n              </div>\n              <h1 className=\"text-5xl md:text-7xl font-bold text-on-surface\">02Bn</h1>\n            </div>\n\n            <h2 className=\"text-3xl md:text-5xl font-bold text-on-surface mb-6\">\n              From Zero to <span className=\"text-primary\">Billions</span>\n            </h2>\n\n            <p className=\"text-xl text-on-surface-variant mb-8 max-w-3xl mx-auto\">\n              Transform your life with the ultimate personal performance tracking and wealth-building platform.\n              Set ambitious goals, build powerful habits, and track your journey to financial freedom.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" asChild>\n                <Link href=\"/auth/signup\">\n                  Get Started Free\n                  <ArrowRightIcon className=\"h-5 w-5\" />\n                </Link>\n              </Button>\n              <Button variant=\"outlined\" size=\"lg\" asChild>\n                <Link href=\"/auth/login\">\n                  Sign In\n                </Link>\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-24 bg-surface\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"text-center mb-16\"\n          >\n            <h3 className=\"text-3xl md:text-4xl font-bold text-on-surface mb-4\">\n              Everything you need to succeed\n            </h3>\n            <p className=\"text-xl text-on-surface-variant max-w-2xl mx-auto\">\n              Comprehensive tools designed to help you achieve your most ambitious goals\n            </p>\n          </motion.div>\n\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {[\n              {\n                icon: ChartBarIcon,\n                title: 'Goal Management',\n                description: 'Set, track, and achieve ambitious financial and personal goals with intelligent milestone breakdown.'\n              },\n              {\n                icon: CurrencyDollarIcon,\n                title: 'Wealth Tracking',\n                description: 'Monitor your net worth, investments, and financial progress with real-time insights and analytics.'\n              },\n              {\n                icon: BookOpenIcon,\n                title: 'Learning System',\n                description: 'Curate knowledge, track reading progress, and build the skills needed for success.'\n              },\n              {\n                icon: HeartIcon,\n                title: 'Habit Building',\n                description: 'Develop powerful daily habits and maintain streaks that compound into extraordinary results.'\n              }\n            ].map((feature, index) => (\n              <motion.div\n                key={feature.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"bg-background rounded-xl p-6 elevation-1 hover:elevation-2 transition-all duration-200\"\n              >\n                <feature.icon className=\"h-12 w-12 text-primary mb-4\" />\n                <h4 className=\"text-xl font-semibold text-on-surface mb-2\">{feature.title}</h4>\n                <p className=\"text-on-surface-variant\">{feature.description}</p>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-24\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n          >\n            <h3 className=\"text-3xl md:text-4xl font-bold text-on-surface mb-4\">\n              Ready to start your journey?\n            </h3>\n            <p className=\"text-xl text-on-surface-variant mb-8\">\n              Join thousands of ambitious individuals who are already building their path to billions.\n            </p>\n            <Button size=\"lg\" asChild>\n              <Link href=\"/auth/signup\">\n                Start Building Your Future\n                <ArrowRightIcon className=\"h-5 w-5\" />\n              </Link>\n            </Button>\n          </motion.div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,MAAM;YACpB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU;;;;;0BAEX,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;sDAEvD,8OAAC;4CAAG,WAAU;sDAAiD;;;;;;;;;;;;8CAGjE,8OAAC;oCAAG,WAAU;;wCAAsD;sDACrD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAG9C,8OAAC;oCAAE,WAAU;8CAAyD;;;;;;8CAKtE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,OAAO;sDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;;oDAAe;kEAExB,8OAAC,2NAAA,CAAA,iBAAc;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAW,MAAK;4CAAK,OAAO;sDAC1C,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;sCAKnE,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM,uNAAA,CAAA,eAAY;oCAClB,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM,mOAAA,CAAA,qBAAkB;oCACxB,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM,uNAAA,CAAA,eAAY;oCAClB,OAAO;oCACP,aAAa;gCACf;gCACA;oCACE,MAAM,iNAAA,CAAA,YAAS;oCACf,OAAO;oCACP,aAAa;gCACf;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;sDACxB,8OAAC;4CAAG,WAAU;sDAA8C,QAAQ,KAAK;;;;;;sDACzE,8OAAC;4CAAE,WAAU;sDAA2B,QAAQ,WAAW;;;;;;;mCATtD,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;0BAiB5B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAGpD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,OAAO;0CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;wCAAe;sDAExB,8OAAC,2NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}]}