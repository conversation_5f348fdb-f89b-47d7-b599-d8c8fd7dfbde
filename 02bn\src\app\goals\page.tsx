'use client'

import { Navigation } from '@/components/ui/Navigation'
import { Card, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Progress } from '@/components/ui/Progress'
import { Badge } from '@/components/ui/Badge'
import { motion } from 'framer-motion'
import { formatCurrency } from '@/lib/utils'
import {
  PlusIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  AcademicCapIcon,
  HeartIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline'

export default function GoalsPage() {
  // Mock data
  const goals = [
    {
      id: 1,
      title: 'Emergency Fund',
      description: 'Build a 6-month emergency fund for financial security',
      category: 'financial',
      target: 30000,
      current: 18500,
      targetDate: '2024-12-31',
      status: 'active',
      priority: 'high'
    },
    {
      id: 2,
      title: 'Investment Portfolio',
      description: 'Grow investment portfolio to $100K',
      category: 'financial',
      target: 100000,
      current: 45000,
      targetDate: '2025-06-30',
      status: 'active',
      priority: 'high'
    },
    {
      id: 3,
      title: 'Side Business Revenue',
      description: 'Generate $5K monthly revenue from side business',
      category: 'business',
      target: 5000,
      current: 2200,
      targetDate: '2024-09-30',
      status: 'active',
      priority: 'medium'
    },
    {
      id: 4,
      title: 'Learn Data Science',
      description: 'Complete advanced data science certification',
      category: 'learning',
      target: 100,
      current: 65,
      targetDate: '2024-08-15',
      status: 'active',
      priority: 'medium'
    },
    {
      id: 5,
      title: 'Fitness Goal',
      description: 'Run a half marathon under 2 hours',
      category: 'health',
      target: 120, // minutes
      current: 135,
      targetDate: '2024-10-15',
      status: 'active',
      priority: 'low'
    }
  ]

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'financial':
        return <CurrencyDollarIcon className="h-5 w-5" />
      case 'business':
        return <BriefcaseIcon className="h-5 w-5" />
      case 'learning':
        return <AcademicCapIcon className="h-5 w-5" />
      case 'health':
        return <HeartIcon className="h-5 w-5" />
      default:
        return <ChartBarIcon className="h-5 w-5" />
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'financial':
        return 'text-google-green'
      case 'business':
        return 'text-google-blue'
      case 'learning':
        return 'text-google-yellow'
      case 'health':
        return 'text-coral-red'
      default:
        return 'text-primary'
    }
  }

  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error'
      case 'medium':
        return 'warning'
      case 'low':
        return 'info'
      default:
        return 'default'
    }
  }

  const formatGoalValue = (goal: any) => {
    if (goal.category === 'financial' || goal.category === 'business') {
      return formatCurrency(goal.current)
    }
    if (goal.category === 'health' && goal.title.includes('marathon')) {
      return `${Math.floor(goal.current / 60)}:${(goal.current % 60).toString().padStart(2, '0')}`
    }
    return `${goal.current}%`
  }

  const formatGoalTarget = (goal: any) => {
    if (goal.category === 'financial' || goal.category === 'business') {
      return formatCurrency(goal.target)
    }
    if (goal.category === 'health' && goal.title.includes('marathon')) {
      return `${Math.floor(goal.target / 60)}:${(goal.target % 60).toString().padStart(2, '0')}`
    }
    return `${goal.target}%`
  }

  return (
    <Navigation>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-on-surface">Goals</h1>
            <p className="text-on-surface-variant mt-1">
              Track your progress towards ambitious targets
            </p>
          </div>
          <Button icon={<PlusIcon className="h-4 w-4" />}>
            Add Goal
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-on-surface">{goals.length}</div>
              <div className="text-sm text-on-surface-variant">Total Goals</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-google-green">
                {goals.filter(g => g.status === 'active').length}
              </div>
              <div className="text-sm text-on-surface-variant">Active</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-google-blue">
                {Math.round(goals.reduce((acc, goal) => acc + (goal.current / goal.target * 100), 0) / goals.length)}%
              </div>
              <div className="text-sm text-on-surface-variant">Avg Progress</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-google-yellow">
                {goals.filter(g => g.priority === 'high').length}
              </div>
              <div className="text-sm text-on-surface-variant">High Priority</div>
            </CardContent>
          </Card>
        </div>

        {/* Goals Grid */}
        <div className="grid gap-6">
          {goals.map((goal, index) => (
            <motion.div
              key={goal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="hover:elevation-2 transition-all duration-200">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 bg-surface-variant rounded-lg ${getCategoryColor(goal.category)}`}>
                        {getCategoryIcon(goal.category)}
                      </div>
                      <div>
                        <h3 className="font-semibold text-on-surface">{goal.title}</h3>
                        <p className="text-sm text-on-surface-variant">{goal.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getPriorityVariant(goal.priority)} size="sm">
                        {goal.priority}
                      </Badge>
                      <Badge variant="default" size="sm">
                        {goal.category}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-on-surface-variant">Progress</span>
                      <span className="font-medium text-on-surface">
                        {formatGoalValue(goal)} / {formatGoalTarget(goal)}
                      </span>
                    </div>
                    <Progress 
                      value={(goal.current / goal.target) * 100} 
                      color={goal.category === 'financial' ? 'success' : 'primary'}
                    />
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-on-surface-variant">
                        Target Date: {new Date(goal.targetDate).toLocaleDateString()}
                      </span>
                      <span className="text-on-surface-variant">
                        {Math.round((goal.current / goal.target) * 100)}% complete
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </Navigation>
  )
}
