'use client'

import { DashboardWidget } from './DashboardWidget'
import { Progress } from '@/components/ui/Progress'
import { Badge } from '@/components/ui/Badge'
import { formatCurrency, formatNumber } from '@/lib/utils'
import { CurrencyDollarIcon, ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/outline'
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, Tooltip } from 'recharts'

interface NetWorthWidgetProps {
  netWorth: number
  change: number
  changePercent: number
  target?: number
  data?: Array<{ month: string; value: number }>
}

export function NetWorthWidget({ 
  netWorth, 
  change, 
  changePercent, 
  target,
  data = []
}: NetWorthWidgetProps) {
  const isPositive = change >= 0
  const progress = target ? (netWorth / target) * 100 : 0

  const mockData = data.length > 0 ? data : [
    { month: 'Jan', value: 95000 },
    { month: 'Feb', value: 102000 },
    { month: 'Mar', value: 108000 },
    { month: 'Apr', value: 115000 },
    { month: 'May', value: 120000 },
    { month: 'Jun', value: 125000 },
  ]

  return (
    <DashboardWidget
      title="Net Worth"
      subtitle="Total assets minus liabilities"
      icon={<CurrencyDollarIcon className="h-5 w-5 text-primary" />}
    >
      <div className="space-y-4">
        {/* Current Value */}
        <div>
          <div className="text-3xl font-bold text-on-surface">
            {formatCurrency(netWorth)}
          </div>
          <div className="flex items-center gap-2 mt-1">
            {isPositive ? (
              <ArrowUpIcon className="h-4 w-4 text-google-green" />
            ) : (
              <ArrowDownIcon className="h-4 w-4 text-error" />
            )}
            <span className={`text-sm font-medium ${isPositive ? 'text-google-green' : 'text-error'}`}>
              {formatCurrency(Math.abs(change))} ({Math.abs(changePercent)}%)
            </span>
            <span className="text-sm text-on-surface-variant">this month</span>
          </div>
        </div>

        {/* Progress to Target */}
        {target && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-on-surface-variant">Progress to {formatCurrency(target)}</span>
              <span className="text-sm font-medium text-on-surface">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} color="primary" />
          </div>
        )}

        {/* Mini Chart */}
        <div className="h-20">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={mockData}>
              <XAxis dataKey="month" hide />
              <YAxis hide />
              <Tooltip 
                formatter={(value: number) => [formatCurrency(value), 'Net Worth']}
                labelStyle={{ color: 'var(--color-on-surface)' }}
                contentStyle={{ 
                  backgroundColor: 'var(--color-surface)',
                  border: '1px solid var(--color-outline-variant)',
                  borderRadius: '8px'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke="var(--color-primary)" 
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, fill: 'var(--color-primary)' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 pt-2 border-t border-outline-variant">
          <div>
            <p className="text-xs text-on-surface-variant">Assets</p>
            <p className="text-sm font-medium text-on-surface">{formatCurrency(netWorth * 1.3)}</p>
          </div>
          <div>
            <p className="text-xs text-on-surface-variant">Liabilities</p>
            <p className="text-sm font-medium text-on-surface">{formatCurrency(netWorth * 0.3)}</p>
          </div>
        </div>
      </div>
    </DashboardWidget>
  )
}
