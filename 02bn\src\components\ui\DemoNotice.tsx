'use client'

import { useState } from 'react'
import { Card, CardContent } from './Card'
import { Button } from './Button'
import { Badge } from './Badge'
import { motion, AnimatePresence } from 'framer-motion'
import { XMarkIcon, InformationCircleIcon } from '@heroicons/react/24/outline'

export function DemoNotice() {
  const [isVisible, setIsVisible] = useState(true)

  if (!isVisible) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md px-4"
      >
        <Card className="border border-google-blue/20 bg-google-blue/5">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <InformationCircleIcon className="h-5 w-5 text-google-blue flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-on-surface">Demo Mode</h4>
                  <Badge variant="info" size="sm">Preview</Badge>
                </div>
                <p className="text-sm text-on-surface-variant">
                  This is a demo version. To use real authentication and data storage, 
                  configure Supabase in your environment variables.
                </p>
              </div>
              <Button
                variant="text"
                size="sm"
                onClick={() => setIsVisible(false)}
                icon={<XMarkIcon className="h-4 w-4" />}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  )
}
