import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";

export const metadata: Metadata = {
  title: "02Bn - Zero to Billion",
  description: "A comprehensive personal performance tracking and wealth-building application",
  keywords: ["wealth building", "goal tracking", "habit tracking", "financial planning", "personal development"],
  authors: [{ name: "02Bn Team" }],
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#6750a4",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body className="h-full font-sans antialiased">
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
