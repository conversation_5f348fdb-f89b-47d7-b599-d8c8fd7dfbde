'use client'

import { forwardRef, useState } from 'react'
import { cn } from '@/lib/utils'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helperText?: string
  startIcon?: React.ReactNode
  endIcon?: React.ReactNode
  variant?: 'outlined' | 'filled'
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      label,
      error,
      helperText,
      startIcon,
      endIcon,
      variant = 'outlined',
      disabled,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false)
    const [isFocused, setIsFocused] = useState(false)

    const isPassword = type === 'password'
    const inputType = isPassword && showPassword ? 'text' : type

    const baseClasses = 'w-full px-4 py-3 text-sm transition-all duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed'

    const variantClasses = {
      outlined: cn(
        'border rounded-lg bg-transparent',
        error
          ? 'border-error focus:border-error focus:ring-2 focus:ring-error/20'
          : 'border-outline focus:border-primary focus:ring-2 focus:ring-primary/20',
        isFocused && !error && 'border-primary ring-2 ring-primary/20'
      ),
      filled: cn(
        'rounded-t-lg border-b-2 bg-surface-variant',
        error
          ? 'border-b-error focus:border-b-error'
          : 'border-b-outline focus:border-b-primary',
        isFocused && !error && 'border-b-primary'
      )
    }

    const labelClasses = cn(
      'absolute left-4 transition-all duration-200 pointer-events-none',
      variant === 'outlined'
        ? cn(
            'bg-surface px-1',
            isFocused || props.value
              ? 'top-0 -translate-y-1/2 text-xs'
              : 'top-1/2 -translate-y-1/2 text-sm',
            error ? 'text-error' : isFocused ? 'text-primary' : 'text-on-surface-variant'
          )
        : cn(
            isFocused || props.value
              ? 'top-2 text-xs'
              : 'top-1/2 -translate-y-1/2 text-sm',
            error ? 'text-error' : isFocused ? 'text-primary' : 'text-on-surface-variant'
          )
    )

    return (
      <div className="relative">
        <div className="relative">
          {startIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-on-surface-variant">
              {startIcon}
            </div>
          )}
          
          <input
            type={inputType}
            className={cn(
              baseClasses,
              variantClasses[variant],
              startIcon && 'pl-10',
              (endIcon || isPassword) && 'pr-10',
              className
            )}
            ref={ref}
            disabled={disabled}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...props}
          />

          {label && (
            <label className={labelClasses}>
              {label}
            </label>
          )}

          {isPassword && (
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-on-surface-variant hover:text-on-surface"
              onClick={() => setShowPassword(!showPassword)}
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeSlashIcon className="h-5 w-5" />
              ) : (
                <EyeIcon className="h-5 w-5" />
              )}
            </button>
          )}

          {endIcon && !isPassword && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-on-surface-variant">
              {endIcon}
            </div>
          )}
        </div>

        {(error || helperText) && (
          <p className={cn(
            'mt-1 text-xs',
            error ? 'text-error' : 'text-on-surface-variant'
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    )
  }
)

Input.displayName = 'Input'

export { Input }
