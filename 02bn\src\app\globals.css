@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import "tailwindcss";

:root {
  /* Light theme colors - Material Design 3 */
  --background: #fefbff;
  --foreground: #1d1b20;
  --surface: #fef7ff;
  --surface-variant: #e7e0ec;
  --on-surface: #1d1b20;
  --on-surface-variant: #49454f;
  --primary: #6750a4;
  --on-primary: #ffffff;
  --primary-container: #eaddff;
  --on-primary-container: #21005d;
  --secondary: #625b71;
  --on-secondary: #ffffff;
  --secondary-container: #e8def8;
  --on-secondary-container: #1d192b;
  --tertiary: #7d5260;
  --on-tertiary: #ffffff;
  --tertiary-container: #ffd8e4;
  --on-tertiary-container: #31111d;
  --error: #ba1a1a;
  --on-error: #ffffff;
  --error-container: #ffdad6;
  --on-error-container: #410002;
  --outline: #79747e;
  --outline-variant: #cab6d0;
  --shadow: #000000;
  --scrim: #000000;
  --inverse-surface: #322f35;
  --inverse-on-surface: #f5eff7;
  --inverse-primary: #d0bcff;

  /* Google signature colors */
  --google-blue: #4285f4;
  --google-red: #ea4335;
  --google-yellow: #fbbc04;
  --google-green: #34a853;
  --coral-red: #ff6b6b;
  --soft-gold: #ffd93d;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-surface: var(--surface);
  --color-surface-variant: var(--surface-variant);
  --color-on-surface: var(--on-surface);
  --color-on-surface-variant: var(--on-surface-variant);
  --color-primary: var(--primary);
  --color-on-primary: var(--on-primary);
  --color-primary-container: var(--primary-container);
  --color-on-primary-container: var(--on-primary-container);
  --color-secondary: var(--secondary);
  --color-on-secondary: var(--on-secondary);
  --color-secondary-container: var(--secondary-container);
  --color-on-secondary-container: var(--on-secondary-container);
  --color-tertiary: var(--tertiary);
  --color-on-tertiary: var(--on-tertiary);
  --color-tertiary-container: var(--tertiary-container);
  --color-on-tertiary-container: var(--on-tertiary-container);
  --color-error: var(--error);
  --color-on-error: var(--on-error);
  --color-error-container: var(--error-container);
  --color-on-error-container: var(--on-error-container);
  --color-outline: var(--outline);
  --color-outline-variant: var(--outline-variant);
  --color-shadow: var(--shadow);
  --color-scrim: var(--scrim);
  --color-inverse-surface: var(--inverse-surface);
  --color-inverse-on-surface: var(--inverse-on-surface);
  --color-inverse-primary: var(--inverse-primary);
  --color-google-blue: var(--google-blue);
  --color-google-red: var(--google-red);
  --color-google-yellow: var(--google-yellow);
  --color-google-green: var(--google-green);
  --color-coral-red: var(--coral-red);
  --color-soft-gold: var(--soft-gold);

  --font-sans: 'Roboto', system-ui, -apple-system, sans-serif;
  --font-mono: 'Roboto Mono', ui-monospace, monospace;

  --radius-xs: 4px;
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;
  --radius-3xl: 28px;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Dark theme colors - Material Design 3 */
    --background: #141218;
    --foreground: #e6e0e9;
    --surface: #141218;
    --surface-variant: #49454f;
    --on-surface: #e6e0e9;
    --on-surface-variant: #cab6d0;
    --primary: #d0bcff;
    --on-primary: #381e72;
    --primary-container: #4f378b;
    --on-primary-container: #eaddff;
    --secondary: #ccc2dc;
    --on-secondary: #332d41;
    --secondary-container: #4a4458;
    --on-secondary-container: #e8def8;
    --tertiary: #efb8c8;
    --on-tertiary: #492532;
    --tertiary-container: #633b48;
    --on-tertiary-container: #ffd8e4;
    --error: #ffb4ab;
    --on-error: #690005;
    --error-container: #93000a;
    --on-error-container: #ffdad6;
    --outline: #938f99;
    --outline-variant: #49454f;
    --shadow: #000000;
    --scrim: #000000;
    --inverse-surface: #e6e0e9;
    --inverse-on-surface: #322f35;
    --inverse-primary: #6750a4;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Roboto', system-ui, -apple-system, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Material Design 3 elevation styles */
.elevation-1 {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15);
}

.elevation-2 {
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3), 0px 2px 6px 2px rgba(0, 0, 0, 0.15);
}

.elevation-3 {
  box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.3), 0px 4px 8px 3px rgba(0, 0, 0, 0.15);
}

.elevation-4 {
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.3), 0px 6px 10px 4px rgba(0, 0, 0, 0.15);
}

.elevation-5 {
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.3), 0px 8px 12px 6px rgba(0, 0, 0, 0.15);
}

/* Ripple effect for Material Design */
.ripple {
  position: relative;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
}

.ripple:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.3;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active:before {
  width: 300px;
  height: 300px;
}
