'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { motion, AnimatePresence } from 'framer-motion'
import {
  HomeIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  BookOpenIcon,
  HeartIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  MagnifyingGlassIcon,
  UserCircleIcon
} from '@heroicons/react/24/outline'
import {
  HomeIcon as HomeIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  CurrencyDollarIcon as CurrencyDollarIconSolid,
  BookOpenIcon as BookOpenIconSolid,
  HeartIcon as HeartIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid
} from '@heroicons/react/24/solid'

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  activeIcon: React.ComponentType<{ className?: string }>
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: HomeIcon,
    activeIcon: HomeIconSolid
  },
  {
    name: 'Goals',
    href: '/goals',
    icon: ChartBarIcon,
    activeIcon: ChartBarIconSolid
  },
  {
    name: 'Finance',
    href: '/finance',
    icon: CurrencyDollarIcon,
    activeIcon: CurrencyDollarIconSolid
  },
  {
    name: 'Learning',
    href: '/learning',
    icon: BookOpenIcon,
    activeIcon: BookOpenIconSolid
  },
  {
    name: 'Wellness',
    href: '/wellness',
    icon: HeartIcon,
    activeIcon: HeartIconSolid
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: Cog6ToothIcon,
    activeIcon: Cog6ToothIconSolid
  }
]

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

function Sidebar({ isOpen, onClose }: SidebarProps) {
  const pathname = usePathname()

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-scrim/50 z-40 lg:hidden"
            onClick={onClose}
          />

          {/* Sidebar */}
          <motion.aside
            initial={{ x: -280 }}
            animate={{ x: 0 }}
            exit={{ x: -280 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed left-0 top-0 h-full w-70 bg-surface border-r border-outline-variant z-50 lg:static lg:translate-x-0"
          >
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-outline-variant">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <span className="text-on-primary font-bold text-sm">02</span>
                  </div>
                  <span className="text-xl font-bold text-on-surface">02Bn</span>
                </div>
                <button
                  onClick={onClose}
                  className="lg:hidden p-2 rounded-lg hover:bg-surface-variant"
                >
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>

              {/* Navigation */}
              <nav className="flex-1 p-4">
                <ul className="space-y-2">
                  {navigationItems.map((item) => {
                    const isActive = pathname === item.href
                    const Icon = isActive ? item.activeIcon : item.icon

                    return (
                      <li key={item.name}>
                        <Link
                          href={item.href}
                          onClick={onClose}
                          className={cn(
                            'flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 relative',
                            isActive
                              ? 'bg-primary-container text-on-primary-container'
                              : 'text-on-surface hover:bg-surface-variant'
                          )}
                        >
                          {isActive && (
                            <motion.div
                              layoutId="activeTab"
                              className="absolute inset-0 bg-primary-container rounded-lg"
                              initial={false}
                              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
                            />
                          )}
                          <Icon className="h-5 w-5 relative z-10" />
                          <span className="font-medium relative z-10">{item.name}</span>
                        </Link>
                      </li>
                    )
                  })}
                </ul>
              </nav>

              {/* User Profile */}
              <div className="p-4 border-t border-outline-variant">
                <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-surface-variant cursor-pointer">
                  <UserCircleIcon className="h-8 w-8 text-on-surface-variant" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-on-surface">John Doe</p>
                    <p className="text-xs text-on-surface-variant"><EMAIL></p>
                  </div>
                </div>
              </div>
            </div>
          </motion.aside>
        </>
      )}
    </AnimatePresence>
  )
}

interface TopBarProps {
  onMenuClick: () => void
}

function TopBar({ onMenuClick }: TopBarProps) {
  return (
    <header className="bg-surface border-b border-outline-variant px-4 py-3 lg:px-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={onMenuClick}
            className="lg:hidden p-2 rounded-lg hover:bg-surface-variant"
          >
            <Bars3Icon className="h-5 w-5" />
          </button>
          
          {/* Search */}
          <div className="hidden md:flex items-center gap-2 bg-surface-variant rounded-full px-4 py-2 min-w-80">
            <MagnifyingGlassIcon className="h-5 w-5 text-on-surface-variant" />
            <input
              type="text"
              placeholder="Search..."
              className="bg-transparent border-none outline-none text-sm text-on-surface placeholder-on-surface-variant flex-1"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <button className="p-2 rounded-lg hover:bg-surface-variant relative">
            <BellIcon className="h-5 w-5" />
            <span className="absolute top-1 right-1 w-2 h-2 bg-error rounded-full"></span>
          </button>
          
          <button className="p-2 rounded-lg hover:bg-surface-variant">
            <UserCircleIcon className="h-6 w-6" />
          </button>
        </div>
      </div>
    </header>
  )
}

interface NavigationProps {
  children: React.ReactNode
}

export function Navigation({ children }: NavigationProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="flex h-screen bg-background">
      {/* Desktop Sidebar */}
      <div className="hidden lg:block">
        <Sidebar isOpen={true} onClose={() => {}} />
      </div>

      {/* Mobile Sidebar */}
      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <TopBar onMenuClick={() => setSidebarOpen(true)} />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </div>
  )
}
