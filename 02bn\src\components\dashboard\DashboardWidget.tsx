'use client'

import { forwardRef } from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'
import { 
  EllipsisVerticalIcon, 
  ArrowsPointingOutIcon,
  XMarkIcon 
} from '@heroicons/react/24/outline'

interface DashboardWidgetProps {
  title: string
  subtitle?: string
  icon?: React.ReactNode
  children: React.ReactNode
  className?: string
  onRemove?: () => void
  onExpand?: () => void
  isDragging?: boolean
}

const DashboardWidget = forwardRef<HTMLDivElement, DashboardWidgetProps>(
  ({ title, subtitle, icon, children, className, onRemove, onExpand, isDragging, ...props }, ref) => {
    return (
      <motion.div
        ref={ref}
        className={cn(
          'group',
          isDragging && 'opacity-50 rotate-3 scale-105',
          className
        )}
        layout
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{ duration: 0.2 }}
        {...props}
      >
        <Card className={cn(
          'h-full transition-all duration-200',
          isDragging ? 'elevation-4' : 'elevation-1 hover:elevation-2'
        )}>
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                {icon && (
                  <div className="p-2 bg-primary/10 rounded-lg">
                    {icon}
                  </div>
                )}
                <div>
                  <h3 className="font-semibold text-on-surface">{title}</h3>
                  {subtitle && (
                    <p className="text-sm text-on-surface-variant">{subtitle}</p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                {onExpand && (
                  <Button
                    variant="text"
                    size="sm"
                    onClick={onExpand}
                    icon={<ArrowsPointingOutIcon className="h-4 w-4" />}
                  />
                )}
                {onRemove && (
                  <Button
                    variant="text"
                    size="sm"
                    onClick={onRemove}
                    icon={<XMarkIcon className="h-4 w-4" />}
                  />
                )}
                <Button
                  variant="text"
                  size="sm"
                  icon={<EllipsisVerticalIcon className="h-4 w-4" />}
                />
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            {children}
          </CardContent>
        </Card>
      </motion.div>
    )
  }
)

DashboardWidget.displayName = 'DashboardWidget'

export { DashboardWidget }
